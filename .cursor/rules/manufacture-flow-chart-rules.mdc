---
description: 
globs: ABL-Document-System/Process-Flowcharts/mermaid-sources/*.mmd
alwaysApply: false
---
# Manufacturing Flow Chart Rules
globs: ABL-Document-System/Process-Flowcharts/mermaid-sources/*.mmd

## Execution Steps

1. **Copy Base Template**: Use [vitamin-c-tablet-bilingual.mmd](mdc:ABL-Document-System/Process-Flowcharts/mermaid-sources/vitamin-c-tablet-bilingual.mmd) as optimized bilingual manufacturing flowchart template
2. **Set Vertical Layout**: Use `flowchart TD` to establish vertical three-tier system structure
3. **Establish Three Main Areas**:
   - Material Management Area (top): `direction LR` + gray dashed border
   - Clean Room Area (middle): `direction LR` + blue dashed border (main focus)
   - Post-Processing Area (bottom): `direction LR` + gray dashed border

## Critical Control Points Setup

4. **Place CP1**: Always in Material Management Area (receiving/inspection)
5. **Place CP2, CP3+**: Only in Clean Room processing areas
6. **Use CP Format**: `🔍 CP#` (simplified, no verbose titles)
7. **Apply CP Styling**:
   ```mermaid
   style CPArea fill:#FFF5F5,stroke:#FF4444,stroke-width:3px,stroke-dasharray: 5 5
   ```

## Bilingual Standard Format

8. **Node Format**:
   ```mermaid
   [中文<br/>English]
   {中文判断<br/>English Decision}
   ```

9. **Area Titles**:
   - Horizontal layout: `中文 English` (space-separated)
   - Vertical layout: `中文<br/>English` (line-break separated)

10. **Connection Labels**:
    ```mermaid
    -->|中文<br/>English|
    ```

## Color Scheme Application

11. **Border Hierarchy** (fixed):
    - Red dashed: CP control areas (highest priority)
    - Blue dashed: Clean room boundary (main focus)
    - Gray-blue solid: Clean room sub-areas
    - Gray dashed: Pre/post processing (de-emphasized)

12. **Node Classifications**:
    - Pass: `fill:#90EE90,stroke:#32CD32`
    - Reject: `fill:#FFB6C1,stroke:#DC143C`
    - Normal: `fill:#F5F5DC,stroke:#8B7355`

## Clean Room Sub-areas

13. **Standard Pattern**:
    ```mermaid
    subgraph WeighingArea [" "]          // No title
    subgraph CPArea ["🔍 CP#"]           // CP areas only
    subgraph PackagingArea [" "]         // No title
    ```

14. **Flow Connection**: 
    ```mermaid
    WeighingArea --> CP2 --> CP3 --> PackagingArea
    ```

## Implementation Checklist

15. **Required Elements Verification**:
    - [ ] Vertical TD layout with 3 main areas
    - [ ] CP1 in material management
    - [ ] CP2+ in clean room only
    - [ ] Bilingual nodes using `<br/>` separation
    - [ ] Color hierarchy: Red CP > Blue Clean > Gray Others
    - [ ] Simplified sub-area titles (CP areas only)

16. **Customizable Elements**:
    - Process-specific steps within areas
    - Number of CP control points (typically 2-4)
    - Clean room sub-area count
    - Specific material/product names

17. **Test and Save**: Use `./generate-flowchart.sh filename.mmd` to test, ensure clean room is focus with everything else supporting or following
