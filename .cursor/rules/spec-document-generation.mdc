---
description: 
globs: ABL-Document-System/Core-Documents/SPEC-Product-Specifications/source/ABL-SPEC-*.md
alwaysApply: false
---
# Product Specification (SPEC) Document Generation Rules

## Execution Steps

1. **Define Document Type**: SPEC documents focus on product quality standards and acceptance criteria, excluding manufacturing instructions or procedures
2. **Set Document Number**: Use ABL-SPEC-XXX format with sequential numbering
3. **Copy Reference Structure**: Use [ABL-SPEC-001-Vitamin-C-Tablet.md](mdc:ABL-Document-System/Core-Documents/SPEC-Product-Specifications/source/ABL-SPEC-001-Vitamin-C-Tablet.md) as template
4. **Create Product Information Section**:
   - Product identification table (product name, strength, dosage form)
   - Raw material quality standards reference table (reference ABL-SPEC-RM-XXX documents)

## Quality Control Specifications Setup

5. **Establish Intermediate Product Quality Standards Table**:
   - Moisture %: ≤X.X (<PERSON> USP <921>)
   - Organoleptic: Description (visual inspection)
   - Test Type: Daily testing
   - CFR Reference: 111.75(a)

6. **Establish Finished Product Quality Standards Table**:
   - Appearance: Description (visual inspection, self-inspection, every batch)
   - Assay: Range% (method, self-inspection, every batch)
   - Microbiological parameters (first three batches + annual monitoring)
   - CFR Reference: 111.75(a)(1)(2)

7. **Set Verification and Storage Requirements**:
   - Finished batch verification specifications table (√N + 1 statistical sampling)
   - Storage and holding requirements table (temperature, humidity, light, container, shelf life)

## Compliance Requirements

8. **Apply FDA cGMP Requirements**:
   - 21 CFR 111.70: Specifications establishment ✓
   - 21 CFR 111.75: Specification verification requirements ✓
   - 21 CFR 111.75(c): Finished batch verification ✓
   - 21 CFR 111.75(d): Packaging specifications ✓
   - 21 CFR 111.113: Testing and examination requirements ✓

9. **Classify Testing Categories**:
   - Self-inspection items: Every batch, in-house testing
   - External testing items: First three batches + annual monitoring
   - Critical Control Points: In-process specifications that must pass before proceeding

## Content Control Checklist

10. **Must Include** ✅:
    - [ ] Product identification (name, strength, dosage form only)
    - [ ] Raw material references (document numbers, not detailed specs)
    - [ ] Quality control specifications (intermediate and finished product)
    - [ ] Testing methods and acceptance criteria
    - [ ] Batch verification requirements
    - [ ] Storage requirements
    - [ ] Packaging specifications

11. **Must NOT Include** ❌:
    - [ ] Detailed product descriptions (belongs in MMR)
    - [ ] Manufacturing procedures (belongs in MMR)
    - [ ] OOS investigation procedures (belongs in SOP)
    - [ ] Change control procedures (belongs in SOP)
    - [ ] Detailed raw material specifications (belongs in separate RM-SPEC files)
    - [ ] Method validation details (belongs in analytical SOPs)

## File Management

12. **File Naming and Storage**:
    - Format: `ABL-SPEC-[XXX]-[Product-Name].md`
    - Source files: `ABL-Document-System/Core-Documents/SPEC-Product-Specifications/source/`
    - PDF output: `ABL-Document-System/Core-Documents/SPEC-Product-Specifications/published/`

13. **Document Integration**:
    - Raw material spec reference format: `| **[Component Name]** | ABL-SPEC-RM-[XXX] [Component Name] Quality Standard |`
    - Related SOP reference format: `*Quality procedures are defined in ABL-SOP-F-003 (OOS Management) and ABL-SOP-E-006 (Change Control)*`

14. **Final Verification**: Ensure document focuses on specification standards, avoid overlap with other document types, maintain practicality over comprehensiveness
