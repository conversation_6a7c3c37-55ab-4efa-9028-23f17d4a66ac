/* ABL Document System Dashboard Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-info {
    display: flex;
    gap: 2rem;
    align-items: center;
    font-size: 0.9rem;
}

.status-ready { color: #2ecc71; }
.status-busy { color: #f39c12; }
.status-error { color: #e74c3c; }

/* Toolbar */
.toolbar {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
}

.toolbar-section h3 {
    margin-bottom: 1rem;
    color: #34495e;
    font-size: 1rem;
    font-weight: 600;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-accent {
    background: #e67e22;
    color: white;
}

.btn-accent:hover {
    background: #d35400;
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 1px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border: 1px solid #bdc3c7;
    background: white;
    color: #34495e;
    border-radius: 4px;
    cursor: pointer;
}

.btn-small:hover {
    background: #ecf0f1;
}

.btn-small.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

/* Document Categories */
.document-categories {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid #3498db;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.category-card[data-type="SOP"] { border-left-color: #3498db; }
.category-card[data-type="SOP-MGMT"] { border-left-color: #2c3e50; }
.category-card[data-type="MMR"] { border-left-color: #e67e22; }
.category-card[data-type="RMS"] { border-left-color: #9b59b6; }
.category-card[data-type="SPEC"] { border-left-color: #2ecc71; }
.category-card[data-type="REC"] { border-left-color: #e74c3c; }

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.category-header h3 {
    font-size: 1.1rem;
    color: #2c3e50;
}

.count {
    background: #ecf0f1;
    color: #7f8c8d;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* File Browser */
.file-browser {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.browser-header {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.title-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.title-row h3 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin: 0;
}


.controls-row-1, .controls-row-2 {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    justify-content: flex-start;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.sort-controls, .filter-controls, .subpart-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.sort-controls label, .filter-controls label, .subpart-controls label {
    font-size: 0.85rem;
    font-weight: 500;
    color: #7f8c8d;
    white-space: nowrap;
}

.sort-controls select, .filter-controls select, .subpart-controls select {
    padding: 0.3rem 0.5rem;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 0.8rem;
    background: white;
    color: #2c3e50;
    cursor: pointer;
    min-width: 120px;
}

.sort-controls select:focus, .filter-controls select:focus, .subpart-controls select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* CFR badges for documents */
.cfr-badge {
    display: inline-block;
    padding: 0.2rem 0.4rem;
    margin-left: 0.5rem;
    font-size: 0.7rem;
    border-radius: 3px;
    font-weight: 600;
}

.cfr-badge.cfr-111 {
    background: #3498db;
    color: white;
}

.cfr-badge.cfr-117 {
    background: #e67e22;
    color: white;
}

.file-grid {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.8rem;
    max-height: 500px;
    overflow-y: auto;
    min-height: 300px;
}

/* List View Styles - Mac-inspired compact design */
.file-list {
    padding: 0;
    display: block;
    max-height: 500px;
    overflow-y: auto;
    min-height: 300px;
}

.file-item-list {
    display: flex;
    align-items: center;
    padding: 0.4rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.1s ease;
    min-height: 32px;
}

.file-item-list:hover {
    background: #f5f7fa;
}

.file-item-list:last-child {
    border-bottom: none;
}

.file-item-list .file-icon {
    font-size: 1rem;
    margin-right: 0.8rem;
    margin-bottom: 0;
    width: 20px;
    text-align: center;
}

.file-item-list .file-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-item-list .file-name {
    font-size: 0.85rem;
    font-weight: 400;
    margin-bottom: 0;
    color: #2c3e50;
}

.file-item-list .file-meta {
    font-size: 0.75rem;
    color: #95a5a6;
    white-space: nowrap;
}

.file-item {
    padding: 0.8rem;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    position: relative;
}

.file-item:hover {
    background: #f8f9fa;
    border-color: #3498db;
    transform: translateY(-1px);
}

.file-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.file-name {
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.3rem;
    word-break: break-word;
}


.file-meta {
    font-size: 0.7rem;
    color: #7f8c8d;
}

/* Console */
.console-section {
    max-width: 1400px;
    margin: 0 auto 2rem;
    padding: 0 2rem;
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.console-header h3 {
    color: #2c3e50;
    font-size: 1.1rem;
}

.console {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.4;
}

.console-line {
    margin-bottom: 0.3rem;
}

.console-line.success { color: #2ecc71; }
.console-line.error { color: #e74c3c; }
.console-line.warning { color: #f39c12; }

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .browser-controls {
        flex-direction: column;
        gap: 0.8rem;
    }
    
    .controls-row-1, .controls-row-2 {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .toolbar {
        padding: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .browser-controls {
        flex-direction: column;
        gap: 0.8rem;
        width: 100%;
    }
    
    .controls-row-1, .controls-row-2 {
        flex-direction: column;
        gap: 0.8rem;
        width: 100%;
    }
    
    .sort-controls, .filter-controls, .subpart-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .sort-controls select, .filter-controls select, .subpart-controls select {
        min-width: 140px;
        flex: 1;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}