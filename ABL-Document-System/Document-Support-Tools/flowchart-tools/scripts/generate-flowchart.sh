#!/bin/bash

# 流程图生成便捷脚本
# 调用 Document_PDF_Tools 文件夹中的流程图工具

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查 pdf-generators 目录是否存在
if [ ! -d "../../pdf-generators" ]; then
    echo -e "${RED}错误: ../../pdf-generators 目录不存在${NC}"
    exit 1
fi

# 进入 pdf-generators 目录并执行
cd "../../pdf-generators"

if [ $# -eq 0 ]; then
    # 没有参数 - 生成所有流程图
    echo -e "${BLUE}🎨 正在生成所有流程图...${NC}"
    ./document-tools.sh flowchart
elif [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    # 显示帮助
    echo "流程图生成工具"
    echo "================"
    echo ""
    echo "用法:"
    echo "  ./generate-flowchart.sh              # 生成所有mermaid文件的流程图"
    echo "  ./generate-flowchart.sh <文件名>     # 生成指定mermaid文件的流程图"
    echo ""
    echo "流程图文件位置:"
    echo "  - 源文件: ../../Process-Flowcharts/mermaid-sources/*.mmd"
    echo "  - 输出: ../../Process-Flowcharts/generated-flowcharts/*.png"
    echo ""
    echo "特性:"
    echo "  ✅ 自动扫描所有 .mmd 文件"
    echo "  ✅ 批量生成流程图"
    echo "  ✅ 支持单文件生成"
    echo "  ✅ 详细的进度显示"
    echo ""
    echo "示例:"
    echo "  ./generate-flowchart.sh"
    echo "  ./generate-flowchart.sh vitamin-c-manufacturing-professional"
    echo "  ./generate-flowchart.sh vitamin-c-manufacturing-professional.mmd"
    echo "  ./generate-flowchart.sh 'vitamin-c-tablet-bilingual.mmd'"
    echo "  ./generate-flowchart.sh '/full/path/to/flowchart.mmd'"
else
    # 有参数 - 生成指定文件
    echo -e "${BLUE}🎨 正在生成指定流程图...${NC}"
    
    # 处理不同类型的路径输入
    if [[ "$1" = /* ]]; then
        # 绝对路径，需要转换为相对路径或文件名
        filename=$(basename "$1")
        ./document-tools.sh flowchart "$filename"
    elif [[ "$1" == *"/"* ]]; then
        # 相对路径，提取文件名
        filename=$(basename "$1")
        ./document-tools.sh flowchart "$filename"
    else
        # 纯文件名，直接传递
        ./document-tools.sh flowchart "$1"
    fi
fi

# 返回原目录
cd ../flowchart-tools/scripts

echo -e "${GREEN}✅ 流程图操作完成！图片文件保存在 Process-Flowcharts/generated-flowcharts/ 目录${NC}" 