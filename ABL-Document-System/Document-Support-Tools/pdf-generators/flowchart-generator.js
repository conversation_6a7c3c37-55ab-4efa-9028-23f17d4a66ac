const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Mermaid流程图生成器
class FlowchartGenerator {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  // 初始化浏览器
  async init() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // 设置基本视口 - 主要用于deviceScaleFactor控制图片质量
    await this.page.setViewport({ 
      width: 1200, 
      height: 800,
      deviceScaleFactor: 1.5 // 控制图片清晰度
    });
  }

  // 关闭浏览器
  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  // 生成流程图
  async generateFlowchart(mermaidCode, outputPath, options = {}) {
    const {
      format = 'png',        // 输出格式: png, svg, pdf
      theme = 'default',     // 主题: default, dark, forest, neutral
      backgroundColor = 'white',
      width = 1200,
      height = 800
    } = options;

    if (!this.browser) {
      await this.init();
    }

    try {
      // 创建包含Mermaid的HTML页面
      const html = this.createMermaidHTML(mermaidCode, theme, backgroundColor);
      
      await this.page.setContent(html);
      
      // 等待Mermaid渲染完成
      await this.page.waitForSelector('#mermaid-diagram svg', { timeout: 10000 });

      // 获取SVG元素的边界框
      const svgElement = await this.page.$('#mermaid-diagram svg');
      const boundingBox = await svgElement.boundingBox();

      console.log(`📊 流程图尺寸: ${Math.round(boundingBox.width)}x${Math.round(boundingBox.height)}`);

      // 动态调整视口大小以确保完整显示
      const padding = 50;
      const newWidth = Math.max(1200, Math.ceil(boundingBox.width) + padding);
      const newHeight = Math.max(800, Math.ceil(boundingBox.height) + padding);

      await this.page.setViewport({
        width: newWidth,
        height: newHeight,
        deviceScaleFactor: 1.5
      });

      // 重新等待渲染完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 确保输出目录存在
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      if (format === 'svg') {
        // 导出SVG
        const svgContent = await this.page.evaluate(() => {
          const svg = document.querySelector('#mermaid-diagram svg');
          return svg.outerHTML;
        });

        fs.writeFileSync(outputPath, svgContent);
      } else if (format === 'pdf') {
        // 导出PDF
        await this.page.pdf({
          path: outputPath,
          width: boundingBox.width + 40,
          height: boundingBox.height + 40,
          printBackground: true
        });
      } else {
        // 导出PNG (默认) - 使用元素截图确保精确尺寸
        const svgElement = await this.page.$('#mermaid-diagram svg');
        await svgElement.screenshot({
          path: outputPath,
          omitBackground: backgroundColor === 'transparent',
          type: 'png'
        });
      }
      
      console.log(`✅ 流程图已生成: ${outputPath}`);
      return outputPath;
      
    } catch (error) {
      console.error('❌ 生成流程图失败:', error);
      throw error;
    }
  }

  // 创建包含Mermaid的HTML页面
  createMermaidHTML(mermaidCode, theme, backgroundColor) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mermaid Flowchart</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: ${backgroundColor};
            font-family: 'Arial', sans-serif;
        }
        #mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }
        .mermaid {
            background-color: ${backgroundColor};
        }
    </style>
</head>
<body>
    <div id="mermaid-diagram">
        <div class="mermaid">
${mermaidCode}
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: '${theme}',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                fontFamily: 'Arial, sans-serif',
                fontSize: '14px'
            }
        });
    </script>
</body>
</html>`;
  }

  // 批量生成流程图
  async generateMultipleFlowcharts(flowcharts, outputDir = './flowcharts') {
    if (!this.browser) {
      await this.init();
    }

    const results = [];
    
    for (const flowchart of flowcharts) {
      const { name, mermaidCode, options = {} } = flowchart;
      const outputPath = path.join(outputDir, `${name}.${options.format || 'png'}`);
      
      try {
        console.log(`📊 生成流程图: ${name}`);
        await this.generateFlowchart(mermaidCode, outputPath, options);
        results.push({ name, path: outputPath, success: true });
      } catch (error) {
        console.error(`❌ 生成 ${name} 失败:`, error.message);
        results.push({ name, error: error.message, success: false });
      }
    }
    
    return results;
  }
}

// 预定义的SOP流程图模板 (已清空)
const SOPFlowchartTemplates = {};

// 命令行工具函数
async function generateSOPFlowchart(templateName, outputPath, options = {}) {
  const generator = new FlowchartGenerator();
  
  try {
    const mermaidCode = SOPFlowchartTemplates[templateName];
    if (!mermaidCode) {
      throw new Error(`未找到模板: ${templateName}`);
    }
    
    console.log(`📊 生成SOP流程图: ${templateName}`);
    await generator.generateFlowchart(mermaidCode, outputPath, options);
    
  } finally {
    await generator.close();
  }
}

// 从文件生成流程图
async function generateFlowchartFromFile(mermaidFile, outputPath, options = {}) {
  const generator = new FlowchartGenerator();
  
  try {
    if (!fs.existsSync(mermaidFile)) {
      throw new Error(`Mermaid文件不存在: ${mermaidFile}`);
    }
    
    const mermaidCode = fs.readFileSync(mermaidFile, 'utf8');
    console.log(`📊 从文件生成流程图: ${mermaidFile}`);
    await generator.generateFlowchart(mermaidCode, outputPath, options);
    
  } finally {
    await generator.close();
  }
}

// 生成所有SOP模板流程图
async function generateAllSOPFlowcharts(outputDir = './flowcharts') {
  const generator = new FlowchartGenerator();
  
  try {
    const flowcharts = Object.keys(SOPFlowchartTemplates).map(name => ({
      name,
      mermaidCode: SOPFlowchartTemplates[name],
      options: { format: 'png', theme: 'default' }
    }));
    
    console.log(`📊 批量生成 ${flowcharts.length} 个SOP流程图...`);
    const results = await generator.generateMultipleFlowcharts(flowcharts, outputDir);
    
    console.log('\n📋 生成结果:');
    results.forEach(result => {
      if (result.success) {
        console.log(`  ✅ ${result.name}: ${result.path}`);
      } else {
        console.log(`  ❌ ${result.name}: ${result.error}`);
      }
    });
    
    return results;
    
  } finally {
    await generator.close();
  }
}

// 导出
module.exports = {
  FlowchartGenerator,
  SOPFlowchartTemplates,
  generateSOPFlowchart,
  generateFlowchartFromFile,
  generateAllSOPFlowcharts
};

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Mermaid流程图生成工具');
    console.log('===================');
    console.log('');
    console.log('用法:');
    console.log('  node flowchart-generator.js file <mermaid文件> <输出文件>');
    console.log('');
    console.log('示例:');
    console.log('  node flowchart-generator.js file ../../Process-Flowcharts/mermaid-sources/my-flowchart.mmd ../../Process-Flowcharts/generated-flowcharts/output.png');
    console.log('');
    console.log('支持的输出格式: png, svg, pdf');
    process.exit(1);
  }
  
  const command = args[0];
  
  if (command === 'file') {
    const mermaidFile = args[1];
    const outputPath = args[2];
    
    if (!mermaidFile || !outputPath) {
      console.error('❌ 请提供Mermaid文件和输出文件路径');
      process.exit(1);
    }
    
    generateFlowchartFromFile(mermaidFile, outputPath).catch(console.error);
    
  } else {
    console.error('❌ 未知命令:', command);
    console.error('使用 "node flowchart-generator.js" 查看帮助信息');
    process.exit(1);
  }
} 