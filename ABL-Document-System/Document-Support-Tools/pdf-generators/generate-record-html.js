const fs = require('fs');
const path = require('path');

// 检测记录文档类型
function detectRecordType(fileName) {
  const baseName = path.basename(fileName, '.md');
  
  if (baseName.match(/ABL-REC-\d+/)) {
    return 'record';
  }
  
  return 'unknown';
}

// 从文件名解析记录文档信息
function parseRecordFileName(fileName) {
  const baseName = path.basename(fileName, '.md');
  const docType = detectRecordType(fileName);
  
  if (docType === 'record') {
    const parts = baseName.split('-');
    // ABL-REC-001-Training-Record => ABL-REC-001
    const docNumber = parts.slice(0, 3).join('-');
    const docTitle = parts.slice(3).join(' ').replace(/-/g, ' ');
    
    return { docType: 'record', docNumber, docTitle };
  }
  
  return { docType: 'unknown', docNumber: '', docTitle: '' };
}

// 提取YAML frontmatter
function extractYAMLMetadata(markdownContent) {
  const yamlMatch = markdownContent.match(/^---\n([\s\S]*?)\n---/);
  if (!yamlMatch) return {};
  
  const yamlContent = yamlMatch[1];
  const metadata = {};
  
  yamlContent.split('\n').forEach(line => {
    const match = line.match(/^(\w+):\s*["']?([^"']+)["']?$/);
    if (match) {
      metadata[match[1]] = match[2];
    }
  });
  
  return metadata;
}

// 提取样式标签
function extractStyleContent(markdown) {
  const styleMatch = markdown.match(/<style>([\s\S]*?)<\/style>/);
  return styleMatch ? styleMatch[1] : '';
}

// 转换图片为Base64
async function convertImagesToBase64(htmlContent, markdownDir) {
  let modifiedContent = htmlContent;
  
  // 匹配所有img标签
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/g;
  let match;
  
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const imgSrc = match[1];
    
    // 跳过已经是Base64的图片
    if (imgSrc.startsWith('data:')) continue;
    
    // 跳过HTTP/HTTPS链接
    if (imgSrc.startsWith('http://') || imgSrc.startsWith('https://')) continue;
    
    try {
      // 构建图片的完整路径
      let imagePath;
      if (path.isAbsolute(imgSrc)) {
        imagePath = imgSrc;
      } else {
        imagePath = path.resolve(markdownDir, imgSrc);
      }
      
      // 检查文件是否存在
      if (!fs.existsSync(imagePath)) {
        console.warn(`图片文件不存在: ${imagePath}`);
        continue;
      }
      
      // 读取图片文件
      const imageBuffer = fs.readFileSync(imagePath);
      const imageExt = path.extname(imagePath).toLowerCase();
      
      // 确定MIME类型
      let mimeType;
      switch (imageExt) {
        case '.png': mimeType = 'image/png'; break;
        case '.jpg':
        case '.jpeg': mimeType = 'image/jpeg'; break;
        case '.gif': mimeType = 'image/gif'; break;
        case '.svg': mimeType = 'image/svg+xml'; break;
        case '.webp': mimeType = 'image/webp'; break;
        default:
          console.warn(`不支持的图片格式: ${imageExt}`);
          continue;
      }
      
      // 转换为Base64
      const base64Data = imageBuffer.toString('base64');
      const base64Src = `data:${mimeType};base64,${base64Data}`;
      
      // 替换原始src
      modifiedContent = modifiedContent.replace(imgSrc, base64Src);
      
      console.log(`图片转换成功: ${imagePath} -> Base64`);
    } catch (error) {
      console.warn(`转换图片失败 ${imgSrc}:`, error.message);
    }
  }
  
  return modifiedContent;
}

// 简单的Markdown转HTML (保留HTML标签，但移除style标签)
function markdownToHtml(markdown) {
  let html = markdown;
  
  // 移除YAML frontmatter
  html = html.replace(/^---\n[\s\S]*?\n---\n/, '');
  
  // 移除style标签（已经单独提取了）
  html = html.replace(/<style>[\s\S]*?<\/style>/g, '');
  
  // 简单的处理：只保留已有的HTML标签，不进行Markdown转换
  // 因为REC文档主要是HTML内容
  return html.trim();
}

// 生成记录文档HTML
async function generateRecordHTML(inputFile, outputFile) {
  try {
    console.log(`开始生成记录文档HTML: ${inputFile} -> ${outputFile}`);
    
    // 读取Markdown文件
    const markdownContent = fs.readFileSync(inputFile, 'utf-8');
    const markdownDir = path.dirname(inputFile);
    
    // 提取YAML元数据
    const metadata = extractYAMLMetadata(markdownContent);
    const { docType, docNumber, docTitle } = parseRecordFileName(inputFile);
    
    // 提取样式内容
    const customStyles = extractStyleContent(markdownContent);
    
    // 转换Markdown为HTML
    let htmlContent = markdownToHtml(markdownContent);
    
    // 转换图片为Base64
    htmlContent = await convertImagesToBase64(htmlContent, markdownDir);
    
    // 创建完整的HTML文档 - 模仿原始文件结构
    const fullHtml = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${metadata.doc_title || docTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 来自Markdown文件的自定义样式 */
        ${customStyles}
        
        /* Header表格样式 */
        .header-table {
            border: 1px solid #ccc;
            margin-bottom: 15px;
            width: 190mm;
            border-collapse: collapse;
        }
        
        .header-table td {
            background-color: #f8f8f8;
            font-weight: normal;
            font-size: 10px;
            color: #666;
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body for="html-export">
    <div class="crossnote markdown-preview">
        <div class="page-container">
            <!-- 文档头部信息表格 -->
            <table class="header-table">
                <tr>
                    <td style="width: 33%; text-align: left; font-style: italic;">${metadata.doc_title || 'Training Record'}</td>
                    <td style="width: 34%; text-align: center; font-size: 9px;">${metadata.doc_number || docNumber}</td>
                    <td style="width: 33%; text-align: right; font-size: 9px;">Ver. ${metadata.revision || '01'}</td>
                </tr>
            </table>
            
            ${htmlContent.includes('<div class="page-container">') ? 
                htmlContent.replace('<div class="page-container">', '').replace(/<\/div>\s*$/, '') : 
                htmlContent}
        </div>
    </div>
</body>
</html>`;
    
    // 写入HTML文件
    fs.writeFileSync(outputFile, fullHtml, 'utf-8');
    
    console.log(`✅ 记录文档HTML生成成功: ${outputFile}`);
    return true;
    
  } catch (error) {
    console.error(`❌ 生成记录文档HTML失败:`, error);
    return false;
  }
}

// 处理单个记录文档
async function processRecordDocument(inputFile) {
  const inputDir = path.dirname(inputFile);
  const baseName = path.basename(inputFile, '.md');
  
  // 确定输出目录（published文件夹）
  const publishedDir = path.join(path.dirname(inputDir), 'published');
  
  // 创建输出目录（如果不存在）
  if (!fs.existsSync(publishedDir)) {
    fs.mkdirSync(publishedDir, { recursive: true });
  }
  
  const outputFile = path.join(publishedDir, `${baseName}.html`);
  
  return await generateRecordHTML(inputFile, outputFile);
}

// 批量处理记录文档
async function processAllRecordDocuments(sourceDir) {
  if (!fs.existsSync(sourceDir)) {
    console.error(`记录文档源目录不存在: ${sourceDir}`);
    return false;
  }
  
  const files = fs.readdirSync(sourceDir);
  const recordFiles = files.filter(file => 
    file.endsWith('.md') && file.match(/ABL-REC-\d+/)
  );
  
  if (recordFiles.length === 0) {
    console.log('未找到记录文档文件');
    return true;
  }
  
  console.log(`找到 ${recordFiles.length} 个记录文档文件`);
  
  let successCount = 0;
  
  for (const file of recordFiles) {
    const inputFile = path.join(sourceDir, file);
    const success = await processRecordDocument(inputFile);
    if (success) successCount++;
  }
  
  console.log(`记录文档HTML生成完成: ${successCount}/${recordFiles.length} 成功`);
  return successCount === recordFiles.length;
}

// 导出函数
module.exports = {
  generateRecordHTML,
  processRecordDocument,
  processAllRecordDocuments,
  detectRecordType,
  parseRecordFileName
};

// 命令行调用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('用法:');
    console.log('  node generate-record-html.js <input-file>     # 处理单个文件');
    console.log('  node generate-record-html.js <source-dir>     # 处理目录中所有记录文档');
    process.exit(1);
  }
  
  const inputPath = args[0];
  
  if (fs.statSync(inputPath).isDirectory()) {
    processAllRecordDocuments(inputPath);
  } else {
    processRecordDocument(inputPath);
  }
}