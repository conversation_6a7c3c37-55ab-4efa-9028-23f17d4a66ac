module.exports = {
  // PDF 格式设置
  format: 'A4',
  
  // 页边距设置
  margin: {
    top: '3.5cm',
    bottom: '2cm',
    left: '1.2cm',
    right: '1.2cm'
  },
  
  // 显示页眉页脚
  displayHeaderFooter: true,
  
  // 页眉模板 (使用变量占位符)
  headerTemplate: `
    <div style="width: 100%; padding: 0 0.5cm; font-family: 'Times New Roman', serif;">
      <!-- Header Table Structure -->
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #333333;">
        
        <tr>
          <td style="border-bottom: 1px solid #333333; padding: 8px; font-size: 16pt; font-weight: bold; text-align: left; color: #222222;">{{COMPANY_NAME}}</td>
          <td style="border-bottom: 1px solid #333333; border-left: 1px solid #333333; padding: 8px; font-size: 12pt; font-weight: bold; text-align: center; color: #000000; background-color: #FFFFFF; min-height: 20px;">&nbsp;</td>
        </tr>
        
        <tr>
          <td style="width: 60%; padding: 8px; font-weight: bold; font-size: 14pt; vertical-align: middle; border-right: 1px solid #333333; color: #222222;">
          {{SOP_TITLE}}
          </td>
          
          <td style="width: 40%; vertical-align: top; padding: 0;">
            
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="width: 50%; border-bottom: 1px solid #333333; padding: 5px; text-align: center; font-size: 12pt; color: #222222;">
                {{SOP_NUMBER}}
                </td>
                <td style="width: 50%; border-bottom: 1px solid #333333; border-left: 1px solid #333333; padding: 5px; text-align: center; font-size: 12pt; color: #222222;">Rev. {{REVISION}}
                </td>
              </tr>
            </table>
            
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 5px; text-align: center; font-size: 12pt; color: #222222;">Effective Date: {{EFFECTIVE_DATE}}
                </td>
              </tr>
            </table>
            
          </td>
        </tr>
      </table>
    </div>
  `,
  
  // 页脚模板 (使用变量占位符)
  footerTemplate: `
    <div style="width: 100%; padding: 0 1cm; font-family: 'Times New Roman', serif;">
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #333333; background-color: #f8f8f8;">
        <tr>
          <td style="width: 33%; padding: 5px; font-size: 12pt; text-align: left; color: #222222;">{{COMPANY_NAME}}</td>
          <td style="width: 34%; padding: 5px; font-size: 12pt; text-align: center; color: #222222;">{{CONFIDENTIALITY_LEVEL}}</td>
          <td style="width: 33%; padding: 5px; font-size: 12pt; text-align: right; color: #222222;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></td>
        </tr>
      </table>
    </div>
  `,
  
  // 打印背景
  printBackground: true,
  
  // 其他常用设置
  preferCSSPageSize: false,
  landscape: false,
  scale: 1,
  
  // 变量替换函数
  replaceVariables: function(template, variables) {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value);
    }
    return result;
  },
  
  // 获取带变量的配置
  getConfigWithVariables: function(variables) {
    const config = { ...this };
    config.headerTemplate = this.replaceVariables(this.headerTemplate, variables);
    config.footerTemplate = this.replaceVariables(this.footerTemplate, variables);
    return config;
  }
}; 