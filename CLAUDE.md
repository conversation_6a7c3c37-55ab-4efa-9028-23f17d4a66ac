# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a pharmaceutical regulatory compliance documentation system for **American Best Life Inc**, focused on FDA 21 CFR Part 111 (dietary supplement cGMP) compliance. The project generates Standard Operating Procedures (SOPs) and Master Manufacturing Records (MMRs) with professional PDF outputs.

## 🛡️ Code Quality Guidelines

### Key Rules for CSS and JavaScript

1. **CSS Selectors**: Avoid invalid operators like `!=` or `==`
   - Use `:not()` pseudo-class or DOM filtering instead

2. **Error Handling**: Include specific error messages
   - Always show `error.message` to users for debugging

3. **DOM Safety**: Check element existence before accessing properties
   - Use `if (element)` checks for `getElementById` results

4. **Reference**: See `development-guidelines/common-errors.md` for examples

## Development Workflow Principles

- **Commit Practice**: 总是在完成一个request之后commit
- **README Policy**: 项目只能有一个README.md文件，位于根目录。不得在子目录创建README文件，所有说明应整合到根目录README中
- **Code Quality**: 遵循基本代码质量指南