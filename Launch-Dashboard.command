#!/bin/bash

# ABL Document System Dashboard Launcher
# 双击此文件启动可视化界面

echo "🚀 Starting ABL Document System Dashboard..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 切换到dashboard目录
cd "$(dirname "$0")/ABL-Document-System/Document-Support-Tools/document-dashboard"

# 检查Node.js依赖
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies for the first time..."
    npm install
    echo "✅ Dependencies installed!"
fi

# 启动服务器
echo "🖥️  Starting dashboard server..."
npm start &
SERVER_PID=$!

# 等待服务器启动
echo "⏳ Waiting for server to start..."
sleep 3

# 自动打开浏览器
echo "🌐 Opening dashboard in browser..."
open "http://localhost:3000"

echo "✅ Dashboard is now running!"
echo "🔗 URL: http://localhost:3000"
echo "💡 Press Ctrl+C to stop the server"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 等待用户停止服务器
wait $SERVER_PID