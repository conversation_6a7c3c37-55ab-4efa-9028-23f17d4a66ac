#!/bin/bash

# 生成HTML文档脚本
# 支持REC文档类型转换为交互式HTML表单

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# 显示帮助信息
show_help() {
    echo "ABL文档系统 - HTML生成器"
    echo ""
    echo "用法:"
    echo "  $0                              # 生成所有REC文档的HTML"
    echo "  $0 REC                          # 生成所有REC文档的HTML"
    echo "  $0 <file.md>                    # 生成单个文档的HTML"
    echo "  $0 --help                       # 显示此帮助信息"
    echo ""
    echo "支持的文档类型:"
    echo "  REC  - 记录模板 (Records Templates)"
    echo ""
    echo "示例:"
    echo "  $0                              # 生成所有文档"
    echo "  $0 REC                          # 生成所有REC文档"
    echo "  $0 ABL-REC-001-Training-Record.md  # 生成单个文档"
    echo ""
    echo "输出目录:"
    echo "  REC: ABL-Document-System/Core-Documents/REC-Records-Templates/published/"
}

# 检查Node.js依赖
check_dependencies() {
    local pdf_generators_dir="$PROJECT_ROOT/ABL-Document-System/Document-Support-Tools/pdf-generators"
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if [ ! -f "$pdf_generators_dir/package.json" ]; then
        print_error "package.json 不存在，请运行 npm install"
        exit 1
    fi
    
    if [ ! -d "$pdf_generators_dir/node_modules" ]; then
        print_warning "node_modules 不存在，正在安装依赖..."
        cd "$pdf_generators_dir"
        npm install
        if [ $? -ne 0 ]; then
            print_error "依赖安装失败"
            exit 1
        fi
    fi
}

# 生成REC文档HTML
generate_rec_html() {
    local source_path="$1"
    local pdf_generators_dir="$PROJECT_ROOT/ABL-Document-System/Document-Support-Tools/pdf-generators"
    
    print_info "开始生成REC文档HTML..."
    
    cd "$pdf_generators_dir"
    
    if [ -f "$source_path" ]; then
        # 处理单个文件
        print_info "处理单个REC文件: $(basename "$source_path")"
        node generate-record-html.js "$source_path"
    elif [ -d "$source_path" ]; then
        # 处理整个目录
        print_info "处理REC目录: $source_path"
        node generate-record-html.js "$source_path"
    else
        print_error "文件或目录不存在: $source_path"
        return 1
    fi
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "REC文档HTML生成完成"
    else
        print_error "REC文档HTML生成失败 (退出码: $exit_code)"
        return 1
    fi
}

# 生成所有文档HTML
generate_all_html() {
    print_info "开始生成所有支持文档类型的HTML..."
    
    local success_count=0
    local total_count=0
    
    # REC 文档
    local rec_source="$PROJECT_ROOT/ABL-Document-System/Core-Documents/REC-Records-Templates/source"
    if [ -d "$rec_source" ]; then
        print_info "正在处理REC文档..."
        total_count=$((total_count + 1))
        if generate_rec_html "$rec_source"; then
            success_count=$((success_count + 1))
        fi
    else
        print_warning "REC源目录不存在: $rec_source"
    fi
    
    # 显示总结
    print_info "HTML生成完成: $success_count/$total_count 类型成功"
    
    if [ $success_count -eq $total_count ] && [ $total_count -gt 0 ]; then
        print_success "所有文档HTML生成成功！"
        return 0
    else
        print_error "部分文档HTML生成失败"
        return 1
    fi
}

# 处理单个文件
process_single_file() {
    local file_path="$1"
    
    # 转换为绝对路径
    if [[ ! "$file_path" = /* ]]; then
        file_path="$(pwd)/$file_path"
    fi
    
    if [ ! -f "$file_path" ]; then
        print_error "文件不存在: $file_path"
        exit 1
    fi
    
    local filename=$(basename "$file_path")
    
    # 检测文档类型并处理
    if [[ "$filename" =~ ^ABL-REC-[0-9]+ ]]; then
        print_info "检测到REC文档: $filename"
        generate_rec_html "$file_path"
    else
        print_error "不支持的文档类型: $filename"
        print_info "支持的文档类型: ABL-REC-XXX (记录模板)"
        exit 1
    fi
}

# 主函数
main() {
    # 检查依赖
    check_dependencies
    
    # 处理命令行参数
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            # 无参数，生成所有文档
            generate_all_html
            ;;
        REC|rec)
            # 生成REC文档
            local rec_source="$PROJECT_ROOT/ABL-Document-System/Core-Documents/REC-Records-Templates/source"
            generate_rec_html "$rec_source"
            ;;
        *.md)
            # 处理单个Markdown文件
            process_single_file "$1"
            ;;
        *)
            print_error "未知参数: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "HTML生成任务完成"
    else
        print_error "HTML生成任务失败"
    fi
    
    exit $exit_code
}

# 运行主函数
main "$@"