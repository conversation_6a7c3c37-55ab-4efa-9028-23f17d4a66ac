# 维生素C制造流程图测试预览

这个文件用于测试和预览流程图的修改效果。

## 当前版本

```mermaid
flowchart LR
    %% 物料管理区域 - 左侧
    subgraph 物料管理 ["📦 物料管理区"]
        direction TB
        A[原料储存] --> B[来料检验]
        B --> C{检验结果}
        C -->|合格| D[入库]
        C -->|不合格| E[拒收]
        D --> F[领料出库]
    end

    %% 洁净区域 - 中间
    subgraph 洁净区 ["🏭 洁净区"]
        direction LR
        subgraph 称量区 ["称量准备"]
            direction TB
            CP1[🔍 CP1<br/>称量控制点]
            H1[称量维生素C] --> H2[称量山梨糖醇]
            H2 --> H3[称量羧甲基淀粉钠]
            H3 --> H4[称量硬脂酸镁]
            CP1 -.-> H1
        end

        subgraph 混合压片区 ["混合&压片工序"]
            direction LR
            subgraph 混合 ["混合工序"]
                direction TB
                CP2[🔍 CP2<br/>混合控制点]
                I1[初混合<br/>维生素C+山梨糖醇+羧甲基淀粉钠] --> I2[混合25分钟]
                I2 --> I3[加入硬脂酸镁]
                I3 --> I4[终混合5分钟]
                CP2 -.-> I1
            end

            subgraph 压片 ["压片工序"]
                direction TB
                CP3[🔍 CP3<br/>压片控制点]
                J[压片] --> K{压片检查}
                K -->|合格| L[筛选]
                K -->|不合格| M[返工]
                M --> J
                CP3 -.-> J
            end

            混合 --> 压片
        end

        subgraph 包装区 ["内包装"]
            direction TB
            N[金属检测] --> O[内包装]
        end

        称量区 --> 混合压片区
        混合压片区 --> 包装区
    end

    %% 后处理区域 - 右侧
    subgraph 后处理 ["📋 后处理区"]
        direction TB
        P[外包装] --> Q[最终检验]
        Q --> R{最终检验}
        R -->|合格| S[成品入库]
        R -->|不合格| T[不合格品处理]
    end

    %% 主流程连接
    物料管理 --> 洁净区
    洁净区 --> 后处理

    %% 样式设置
    classDef cpControlPoint fill:#FF4444,stroke:#CC0000,stroke-width:3px,color:#FFF,font-weight:bold
    classDef passNode fill:#90EE90,stroke:#32CD32,stroke-width:2px,color:#000
    classDef rejectNode fill:#FFB6C1,stroke:#DC143C,stroke-width:2px,color:#000
    classDef normalNode fill:#F5F5DC,stroke:#8B7355,stroke-width:1px,color:#000
    classDef subgraphStyle fill:#F8F9FA,stroke:#6C757D,stroke-width:2px

    %% 应用样式
    class CP1,CP2,CP3 cpControlPoint
    class D,L,S passNode
    class E,T rejectNode
    class H1,H2,H3,H4,I1,I2,I3,I4,J,K,N,O,P,Q normalNode
```

## 修改说明

- 这个文件用于快速预览和测试流程图修改
- 您可以直接在VSCode中预览mermaid图表
- 修改完成后，我们可以将最终版本同步到 `.mmd` 文件

## 当前问题

中间洁净区的流程需要进一步优化，使其更紧凑和合理。
